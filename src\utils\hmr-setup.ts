/**
 * Configuração global do HMR para o projeto
 * Este arquivo deve ser importado no ponto de entrada da aplicação
 */

// Configuração global para React Fast Refresh
if (process.env.NODE_ENV === 'development') {
  // Habilita logs detalhados do HMR
  if (module.hot) {
    module.hot.accept();

    // Log quando o HMR é ativado
    console.log('🔥 HMR ativado para desenvolvimento');

    // Configuração para melhor debugging
    module.hot.dispose(() => {
      console.log('🔄 HMR: Limpando módulo anterior');
    });

    // Aceita atualizações de todos os módulos filhos
    module.hot.accept(undefined, (err?: Error) => {
      if (err) {
        console.error('❌ HMR: Erro ao aceitar atualização:', err);
      } else {
        console.log('✅ HMR: Atualização aceita com sucesso');
      }
    });
  }

  // Configuração específica para React Fast Refresh
  if (typeof window !== 'undefined') {
    // Força o React Fast Refresh a preservar mais estado
    const originalConsoleError = console.error;
    console.error = (...args) => {
      // Filtra alguns warnings desnecessários do React Fast Refresh
      if (
        typeof args[0] === 'string' &&
        args[0].includes('Warning: React.jsx: type is invalid')
      ) {
        return;
      }
      originalConsoleError.apply(console, args);
    };

    // Adiciona listener para mudanças de arquivo
    if (module.hot) {
      let updateTimeout: NodeJS.Timeout;

      module.hot.addStatusHandler(status => {
        if (status === 'apply') {
          console.log('🚀 Fast Refresh: Aplicando atualizações...');

          // Debounce para evitar múltiplas atualizações
          clearTimeout(updateTimeout);
          updateTimeout = setTimeout(() => {
            console.log('✨ Fast Refresh: Componentes atualizados!');
          }, 100);
        }
      });
    }
  }
}

/**
 * Função para configurar HMR em componentes específicos
 */
export function setupComponentHMR(componentName: string) {
  if (process.env.NODE_ENV === 'development' && module.hot) {
    console.log(`🔧 Configurando HMR para: ${componentName}`);

    module.hot.accept();

    // Registra o componente para debugging
    if (typeof window !== 'undefined') {
      (window as any).__HMR_COMPONENTS__ =
        (window as any).__HMR_COMPONENTS__ || [];
      (window as any).__HMR_COMPONENTS__.push(componentName);
    }
  }
}

/**
 * Função para verificar se o HMR está funcionando
 */
export function checkHMRStatus() {
  if (process.env.NODE_ENV === 'development') {
    console.log('🔍 Status do HMR:');
    console.log('- module.hot:', !!module.hot);
    console.log('- React Fast Refresh:', !!(window as any)?.$RefreshReg$);
    console.log(
      '- Componentes registrados:',
      (window as any)?.__HMR_COMPONENTS__ || [],
    );
  }
}

// Auto-executa a configuração quando o módulo é carregado
if (process.env.NODE_ENV === 'development') {
  setupComponentHMR('HMR-Setup');
}

export default {
  setupComponentHMR,
  checkHMRStatus,
};
